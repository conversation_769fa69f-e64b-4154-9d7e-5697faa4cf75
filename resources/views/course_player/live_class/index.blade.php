<div class="tab-pane p-4 fade @if($tab == 'live-class') show active @endif" id="pills-live-class" role="tabpanel" aria-labelledby="pills-live-class-tab" tabindex="0">
    @if(!$enroll_status)
        <div class="purchase-notification text-center">
            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/graduation-cap.svg') }}" alt="" width="60" height="60">
            <h4>Mua khóa học để tham gia lớp học trực tuyến</h4>
            <p>Bạn cần mua khóa học để có thể tham gia các buổi học trực tuyến và tương tác với giảng viên.</p>
            <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#modal-regis">
                <img src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}" alt="" width="16" height="16" style="transform: translateY(-2px);">
                Mua khóa học ngay
            </button>
        </div>
    @else
        <div class="row">
            <div class="col-md-12">
                <h6>{{ get_phrase('Class Schedules') }}:</h6>
            </div>
            <div class="col-md-12">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <th>#</th>
                            <th>{{ get_phrase('Topic') }}</th>
                            <th>{{ get_phrase('Date & time') }}</th>
                            <th>{{ get_phrase('Action') }}</th>
                        </thead>
                        <tbody>

                            @foreach (App\Models\Live_class::where('course_id', $course_details->id)->get() as $key => $live_class)
                                <tr>
                                    <td>{{ ++$key }}</td>
                                    <td>
                                        {{ $live_class->class_topic }}
                                    </td>
                                    <td>{{ date('d M Y - h:i A', strtotime($live_class->class_date_and_time)) }}</td>
                                    <td>
                                        <a href="{{ route('live.class.join', ['id' => $live_class->id]) }}"
                                            class="btn py-0 ps-1 pe-1 text-dark" data-bs-toggle="tooltip"
                                            data-bs-title="{{ get_phrase('Join Now') }}"><i
                                                class="fi-rr-video-camera"></i></a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif
</div>
